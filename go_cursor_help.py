import platform
import os
import subprocess
import requests
import time
import urllib3
from pathlib import Path
from dotenv import load_dotenv
from logger import logging
from language import get_translation

# 禁用不安全请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 加载环境变量
load_dotenv()

def download_script(url, file_path, proxy=None):
    """下载脚本到指定路径"""
    try:
        proxies = {}
        if proxy:
            proxies = {
                "http": proxy,
                "https": proxy
            }
            logging.info(get_translation("using_proxy", proxy=proxy))

        # 使用 verify=True 进行安全连接，如果代理需要，可以设置为 False
        verify = False if proxy else True

        response = requests.get(url, proxies=proxies, timeout=30, verify=verify)
        response.raise_for_status()

        with open(file_path, 'wb') as f:
            f.write(response.content)

        # 确保文件有执行权限（对于Unix系统）
        if platform.system() in ["Darwin", "Linux"]:
            os.chmod(file_path, 0o755)

        return True
    except Exception as e:
        logging.error(f"下载脚本失败: {str(e)}")
        return False

def go_cursor_help():
    system = platform.system()
    logging.info(get_translation("current_operating_system", system=system))

    # 创建缓存目录（在当前项目下）
    cache_dir = Path(os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache"))
    cache_dir.mkdir(exist_ok=True)

    # 确定脚本文件名和路径
    script_filename = {
        "Darwin": "cursor_mac_id_modifier.sh",
        "Linux": "cursor_linux_id_modifier.sh",
        "Windows": "cursor_win_id_modifier.ps1"
    }.get(system)

    if not script_filename:
        logging.error(get_translation("unsupported_operating_system", system=system))
        return False

    script_cache_path = cache_dir / script_filename

    # 获取代理设置
    proxy = os.getenv("BROWSER_PROXY")

    # 原始GitHub链接
    github_url = f"https://raw.githubusercontent.com/yuaotian/go-cursor-help/master/scripts/run/{script_filename}"
    # 加速链接
    accelerated_url = f"https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/{script_filename}"

    # 根据条件决定下载方式
    if proxy:
        # 使用代理下载到缓存
        logging.info(get_translation("downloading_with_proxy"))
        download_success = download_script(github_url, script_cache_path, proxy)
        if not download_success:
            logging.error(get_translation("proxy_download_failed"))
            return False

        # 使用缓存的脚本
        logging.info(get_translation("using_cached_script"))
    elif script_cache_path.exists():
        # 使用已缓存的脚本
        cache_time = time.ctime(os.path.getmtime(script_cache_path))
        logging.warning("⚠️ " + get_translation("using_cached_script_warning", time=cache_time))
    else:
        # 没有代理且没有缓存，使用加速链接
        logging.warning("⚠️⚠️⚠️ " + get_translation("using_accelerated_url_warning"))
        logging.warning("⚠️⚠️⚠️ " + get_translation("security_risk_warning"))

        user_confirm = input(get_translation("confirm_continue") + " (y/n): ")
        if user_confirm.lower() != 'y':
            logging.info(get_translation("operation_cancelled"))
            return False

        download_success = download_script(accelerated_url, script_cache_path)
        if not download_success:
            logging.error(get_translation("accelerated_download_failed"))
            return False

        logging.info(get_translation("script_cached"))

    # 执行脚本
    if system == "Darwin":  # macOS
        cmd = f'sudo bash "{script_cache_path}"'
        logging.info(get_translation("executing_macos_command"))
        os.system(cmd)
    elif system == "Linux":
        cmd = f'sudo bash "{script_cache_path}"'
        logging.info(get_translation("executing_linux_command"))
        os.system(cmd)
    elif system == "Windows":
        cmd = f'powershell -ExecutionPolicy Bypass -File "{script_cache_path}"'
        logging.info(get_translation("executing_windows_command"))
        subprocess.run(cmd, shell=True)

    return True

def main():
    go_cursor_help()

if __name__ == "__main__":
    main()
