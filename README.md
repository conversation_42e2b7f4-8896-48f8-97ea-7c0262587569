# Cursor Pro 自动化工具

## 项目说明

本项目基于 [@chengazhen/cursor-auto-free](https://github.com/chengazhen/cursor-auto-free) 开源项目进行二次开发，在此特别感谢原作者的无私分享和贡献！

## 功能改进

在原项目基础上，本工具主要解决了以下问题：

1. 解决部分用户首次打开就需要验证的问题
2. 解决注册完成需要重新登录的问题
3. 新增仅注册功能，可保存账号信息到JSON文件
4. 新增禁止Cursor自动更新功能
5. 新增选择已保存账号并一键应用功能
6. 优化了整体使用体验

## 功能演示

![功能演示](./screen/image.png)

## 功能说明

### 1. 重置机器码
仅重置机器码，适用于已有Cursor账号但需要刷新机器码的用户。

### 2. 完整注册流程
注册新账号并自动重置机器码，一步到位完成全部设置。

### 3. 仅注册
注册新账号并保存账号信息（邮箱、密码、token）到JSON文件，方便后续手动登录。

### 4. 禁止自动更新
禁用Cursor的自动更新功能，避免更新后需要重新激活的问题，保持软件稳定运行。

### 5. 选择已保存账号并应用
列出之前注册并保存的所有账号，选择其中一个自动应用并重置机器码，无需再次注册。

## 使用指南

[详细使用文档](https://cursor-auto-free-doc.vercel.app)

## QQ交流群（有问题先发qq群，qq群会及时发最新版本）

QQ群号：996321868

![公众号](./screen/qrcode_for_gh_c985615b5f2b_258.jpg)

## 声明

- 本项目仅供学习交流使用，请勿用于商业用途
- 本项目不承担任何法律责任，使用本项目造成的任何后果，由使用者自行承担
- 本项目采用 [CC BY-NC-ND 4.0](https://creativecommons.org/licenses/by-nc-nd/4.0/) 许可证

## 英文名字集

本项目使用的英文名字数据集来源: [usa-names-dataset](https://github.com/toniprada/usa-names-dataset)

## 支持作者

开源不易，如果这个项目对你有所帮助，可以请我喝杯咖啡：

<img src="./screen/image copy.png" width="300" alt="支付宝收款码"/>
<img src="./screen/image copy 2.png" width="300" alt="微信收款码"/>

## 特别鸣谢

- [chengazhen/cursor-auto-free](https://github.com/chengazhen/cursor-auto-free) - 本项目的基础，提供了 Cursor Pro 自动化的核心功能
- [linuxDo](https://linux.do/) - 一个真正的技术社区，提供了宝贵的支持和帮助


